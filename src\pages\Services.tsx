import { useTranslation } from "react-i18next";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useMemo } from "react";
import BasicHeader from "@/components/layout/BasicHeader";
import Footer from "@/components/layout/Footer";
import { ServiceCard } from "@/components/ServiceCard";
import Technologies from "@/components/sections/Technologies";
import CTA from "@/components/sections/CTA";
import { Button } from "@/components/ui/button";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { ServiceDetails } from "@/components/services/ServiceDetails";

import ProcessStepsSection from "@/components/services/ProcessStepsSection";
import { homePageServices, processSteps } from "@/data/services-data";

// Demo code snippet for the hero section
const codeSnippet = `// Modern React component with TypeScript
import { useState, useEffect } from 'react';
import { fetchData } from '@/api/services';

interface DataProps {
  id: string;
  title: string;
  status: 'active' | 'pending';
}

export const DataComponent = () => {
  const [data, setData] = useState<DataProps[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const result = await fetchData();
        setData(result);
      } catch (error) {
        // Handle error silently or show user-friendly message
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <div className="container mx-auto p-4">
      {loading ? (
        <p>Loading data...</p>
      ) : (
        <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {data.map((item) => (
            <li key={item.id} className="p-4 border rounded-lg">
              <h3 className="text-lg font-bold">{item.title}</h3>
              <span className={\`px-2 py-1 rounded \${
                item.status === 'active'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }\`}>
                {item.status}
              </span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};`;

const Services = () => {
  const { t } = useTranslation();
  const { slug } = useParams();

  // Memoize the selected service to prevent unnecessary re-renders
  const selectedService = useMemo(() =>
    slug ? homePageServices.find(service => service.id === slug) : null,
    [slug]
  );

  return (
    <div className="min-h-screen flex flex-col">
      <ErrorBoundary>
        <BasicHeader />
        <main className="flex-grow pt-20">
          {/* Hero Section with Code Snippet */}
          <section className="py-12 sm:py-16 md:py-24 bg-muted/30 dark:bg-muted/10 overflow-hidden relative">
            {/* Background decorations */}
            <div className="absolute top-1/4 left-0 w-60 sm:w-72 h-60 sm:h-72 bg-primary/5 rounded-full blur-3xl -z-10"></div>
            <div className="absolute bottom-1/4 right-0 w-60 sm:w-80 h-60 sm:h-80 bg-secondary/5 rounded-full blur-3xl -z-10"></div>

            <div className="container">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 items-center">
                <div>
                  <span className="inline-block text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1 mb-3 sm:mb-4">
                    {t('services.hero.title')}
                  </span>
                  <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-foreground">
                    {t('services.hero.subtitle')}
                  </h1>
                  <p className="text-lg sm:text-xl text-muted-foreground mb-6 sm:mb-8">
                    {t('home.services.subtitle')}
                  </p>
                  <div className="flex flex-wrap gap-3 sm:gap-4">
                    <Button size="lg" className="text-sm sm:text-base" asChild>
                      <Link to="/contact">{t('common.contactUs')}</Link>
                    </Button>
                    <Button size="lg" variant="outline" className="text-sm sm:text-base" asChild>
                      <Link to="/about">{t('common.learnMore')}</Link>
                    </Button>
                  </div>
                </div>

                <div className="relative mt-8 lg:mt-0">
                  <div className="bg-card rounded-xl border border-border p-3 sm:p-4 shadow-lg overflow-hidden">
                    <div className="flex items-center justify-between mb-2 px-2">
                      <div className="flex space-x-2">
                        <div className="w-3 h-3 rounded-full bg-red-500"></div>
                        <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      </div>
                      <div className="text-xs text-muted-foreground">App.tsx</div>
                    </div>
                    <pre className="text-xs md:text-sm overflow-x-auto p-2 sm:p-4 bg-muted rounded-lg">
                      <code className="language-typescript">{codeSnippet}</code>
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {selectedService ? (
            <ErrorBoundary>
              <ServiceDetails service={selectedService} />
            </ErrorBoundary>
          ) : (
            <>
              {/* Services Grid */}
              <section className="py-12 sm:py-16 md:py-24">
                <div className="container">
                  <div className="text-center max-w-3xl mx-auto mb-10 sm:mb-16">
                    <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6 text-foreground">
                      {t('services.hero.title')}
                    </h2>
                    <p className="text-base sm:text-lg md:text-xl text-muted-foreground">
                      {t('features.description')}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
                    {homePageServices.map((service) => (
                      <ServiceCard
                        key={service.id}
                        icon={service.icon}
                        title={service.title}
                        description={service.description}
                        features={service.features}
                        id={service.id}
                      />
                    ))}
                  </div>
                </div>
              </section>

              {/* Development Process Section */}
              <ErrorBoundary>
                <ProcessStepsSection processSteps={processSteps} />
              </ErrorBoundary>

              {/* Technologies Section */}
              <Technologies />

              {/* CTA Section */}
              <CTA />
            </>
          )}
        </main>
        <Footer />
      </ErrorBoundary>
    </div>
  );
};

export default Services;
