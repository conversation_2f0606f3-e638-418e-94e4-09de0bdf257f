<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeSafir Digital Ad</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .ad-container {
            width: 1080px;
            height: 1080px;
            background: linear-gradient(135deg, #1A1F71 0%, #0F1357 100%);
            position: relative;
            font-family: 'Cairo', sans-serif;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 40px;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo {
            font-size: 48px;
            font-weight: 900;
            color: #FFFFFF;
            letter-spacing: 2px;
        }

        .logo .code {
            color: #00BFA6;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .main-title {
            font-size: 64px;
            font-weight: 900;
            color: #FFFFFF;
            line-height: 1.2;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .price-section {
            margin-bottom: 40px;
        }

        .old-price {
            font-size: 32px;
            color: #CCCCCC;
            text-decoration: line-through;
            margin-bottom: 10px;
        }

        .new-price {
            font-size: 56px;
            font-weight: 900;
            color: #FFD600;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            background: rgba(255, 214, 0, 0.1);
            padding: 10px 20px;
            border-radius: 15px;
            display: inline-block;
        }

        .features-list {
            list-style: none;
            margin-bottom: 40px;
            text-align: right;
        }

        .features-list li {
            font-size: 28px;
            color: #FFFFFF;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .features-list li::before {
            content: "✓";
            color: #00BFA6;
            font-weight: bold;
            font-size: 32px;
            margin-left: 15px;
        }

        .tagline {
            font-size: 32px;
            color: #FFFFFF;
            font-weight: 600;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .cta-button {
            background: linear-gradient(45deg, #FFD600, #FFC107);
            color: #1A1F71;
            font-size: 36px;
            font-weight: 700;
            padding: 20px 40px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            box-shadow: 0 8px 20px rgba(255, 214, 0, 0.3);
            transition: transform 0.3s ease;
            margin-bottom: 30px;
        }

        .cta-button:hover {
            transform: translateY(-2px);
        }

        .whatsapp-icon {
            width: 40px;
            height: 40px;
            background: #25D366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .footer-info {
            text-align: center;
            font-size: 20px;
            color: #CCCCCC;
            line-height: 1.4;
        }

        .phone-number {
            color: #00BFA6;
            font-weight: 600;
            font-size: 24px;
            margin-top: 10px;
        }

        .devices-bg {
            position: absolute;
            right: -100px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.1;
            z-index: 1;
        }

        .laptop {
            width: 300px;
            height: 200px;
            background: #FFFFFF;
            border-radius: 10px;
            margin-bottom: 20px;
            position: relative;
        }

        .laptop::before {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 320px;
            height: 20px;
            background: #CCCCCC;
            border-radius: 0 0 20px 20px;
        }

        .mobile {
            width: 120px;
            height: 200px;
            background: #FFFFFF;
            border-radius: 20px;
            margin-right: 50px;
        }

        .discount-badge {
            position: absolute;
            top: 80px;
            left: 40px;
            background: #FFD600;
            color: #1A1F71;
            font-size: 28px;
            font-weight: 900;
            padding: 15px 25px;
            border-radius: 50px;
            transform: rotate(-15deg);
            box-shadow: 0 5px 15px rgba(255, 214, 0, 0.4);
        }

        .accent-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 0;
        }

        .shape1 {
            position: absolute;
            width: 200px;
            height: 200px;
            background: rgba(0, 191, 166, 0.1);
            border-radius: 50%;
            top: 100px;
            left: -100px;
        }

        .shape2 {
            position: absolute;
            width: 150px;
            height: 150px;
            background: rgba(255, 214, 0, 0.1);
            border-radius: 50%;
            bottom: 100px;
            right: -75px;
        }
    </style>
</head>
<body>
    <div class="ad-container">
        <div class="accent-shapes">
            <div class="shape1"></div>
            <div class="shape2"></div>
        </div>

        <div class="discount-badge">
            خصم 25%
        </div>

        <div class="devices-bg">
            <div class="laptop"></div>
            <div class="mobile"></div>
        </div>

        <div class="logo-section">
            <div class="logo">
                <span class="code">Code</span>Safir
            </div>
        </div>

        <div class="main-content">
            <h1 class="main-title">
                امتلك موقعك الإلكتروني<br>
                الاحترافي
            </h1>

            <div class="price-section">
                <div class="old-price">بدلاً من 6,000 ج.م</div>
                <div class="new-price">فقط 4,500 ج.م</div>
            </div>

            <ul class="features-list">
                <li>دومين مجاني للسنة الأولى</li>
                <li>استضافة مجانية لمدة سنة</li>
                <li>بريد إلكتروني احترافي باسم شركتك</li>
                <li>دعم فني وصيانة 3 شهور</li>
            </ul>

            <div class="tagline">
                ابدأ اليوم وابقَ سفير على الويب مع CodeSafir
            </div>

            <button class="cta-button">
                <div class="whatsapp-icon">W</div>
                راسلنا على واتساب الآن
            </button>
        </div>

        <div class="footer-info">
            <div>السعر شامل كل شيء: تصميم + دومين + استضافة + بريد احترافي</div>
            <div>الدفع متاح عبر فوري/فيزا/تحويل بنكي</div>
            <div class="phone-number">01XXXXXXXXX</div>
        </div>
    </div>
</body>
</html>
