import { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { Filter, Grid3X3, LayoutGrid, Search } from "lucide-react";
import { motion } from "framer-motion";
import BasicHeader from "@/components/layout/BasicHeader";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import CTA from "@/components/sections/CTA";
import { ProductCard } from "@/components/ProductCard";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import { products, productCategories } from "@/data/products-data";
import { Product, ProductCategory } from "@/types/products";

const Products = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();
  const { slug } = useParams();

  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Filter products based on selected criteria
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      const matchesCategory = selectedCategory === "all" || product.category.id === selectedCategory;
      const matchesSearch = product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFeatured = !showFeaturedOnly || product.featured;

      return matchesCategory && matchesSearch && matchesFeatured;
    });
  }, [selectedCategory, searchQuery, showFeaturedOnly]);

  // Hero Section Component
  const HeroSection = () => (
    <section className="relative py-16 md:py-24 bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      <div className="container">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <span className="inline-block text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-4 py-2 mb-4">
              {t('products.hero.title')}
            </span>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-foreground">
              {t('products.hero.subtitle')}
            </h1>
            <p className="text-lg sm:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              {t('products.hero.description')}
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );

  // Product Categories Section
  const CategoriesSection = () => (
    <section className="py-12 border-b border-border">
      <div className="container">
        <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between mb-8">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold mb-2">{t('products.categories.title')}</h2>
            <p className="text-muted-foreground">{t('products.categories.subtitle')}</p>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <LayoutGrid className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder={t('products.filters.category')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('products.categories.all')}</SelectItem>
              {productCategories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant={showFeaturedOnly ? "default" : "outline"}
            onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
            className="whitespace-nowrap"
          >
            <Filter className="w-4 h-4 mr-2" />
            {t('products.filters.featured')}
          </Button>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-6 mb-8">
            <TabsTrigger value="all" className="text-xs sm:text-sm">
              {t('products.categories.all')}
            </TabsTrigger>
            {productCategories.map((category) => (
              <TabsTrigger key={category.id} value={category.id} className="text-xs sm:text-sm">
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>
    </section>
  );

  return (
    <div className="min-h-screen bg-background">
      <BasicHeader />
      <main>
        <HeroSection />
        <CategoriesSection />

        {/* Products Grid */}
        <section className="py-16">
          <div className="container">
            {filteredProducts.length === 0 ? (
              <div className="text-center py-16">
                <div className="w-16 h-16 rounded-full bg-muted/20 flex items-center justify-center mx-auto mb-4">
                  <Search className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">No products found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your search criteria or browse all products.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedCategory("all");
                    setShowFeaturedOnly(false);
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            ) : (
              <div className="space-y-8">
                {/* Results Header */}
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold">
                      {selectedCategory === "all"
                        ? t('products.categories.all')
                        : productCategories.find(cat => cat.id === selectedCategory)?.name
                      }
                    </h2>
                    <p className="text-muted-foreground">
                      {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found
                    </p>
                  </div>
                </div>

                {/* Products Grid/List */}
                <div className={cn(
                  "gap-6",
                  viewMode === "grid"
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                    : "flex flex-col space-y-6"
                )}>
                  {filteredProducts.map((product, index) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <ProductCard
                        product={product}
                        viewMode={viewMode}
                      />
                    </motion.div>
                  ))}
                </div>

                {/* Load More Button (for future pagination) */}
                {filteredProducts.length >= 6 && (
                  <div className="text-center pt-8">
                    <Button variant="outline" size="lg">
                      Load More Products
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </section>

        <CTA />
      </main>
      <Footer />
    </div>
  );
};

export default Products;
