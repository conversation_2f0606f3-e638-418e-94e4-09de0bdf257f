# CodeSafir Changelog

All notable changes to the CodeSafir website project will be documented in this file.

## [Unreleased]

## [0.7.1] - 2024-12-21

### Changed

- **Services Page Text Cleanup**: Removed "Check out some of our recent projects" text from Services page by clearing the home.portfolio.subtitle translation key in both English and Arabic translation files
- **Services Page Structure**: Removed "Our Work" section (CaseStudiesSection component) from Services page to streamline content and focus on core services
- **Navigation Menu Order**: Updated navigation menu to display in the order: Home, About, Services, Products, Portfolio, Contact
- **Products Page Status**: Changed Products page from functional link to "Coming Soon" status with visual indicator badge

### Improved

- **User Experience**: Cleaner Services page layout without redundant text and portfolio content that was duplicating information available on the dedicated Portfolio page
- **Page Focus**: Services page now focuses exclusively on service offerings without distracting portfolio elements
- **Navigation Clarity**: Products section now clearly indicates "Coming Soon" status, preventing user confusion and setting proper expectations
- **Route Management**: Removed Products route from application routing to prevent access to incomplete functionality

## [0.7.0] - 2024-12-21

### Added

- **Products Page Implementation**: Created comprehensive Products page with modern design and professional layout
- **Product Data Structure**: Implemented TypeScript interfaces for products with detailed type safety
- **Product Categories**: Added five main categories (CMS Systems, Browser Extensions, SaaS Products, Web Tools, Mobile Apps)
- **Actual Product Data**: Created detailed information for CodeSafir's real digital products including:
  - **CodeSafir CMS**: Headless content management system
  - **DevTools Pro Extension**: Chrome extension for web developers
  - **ProjectFlow SaaS**: Cloud-based project management platform
  - **CodeFormat Pro**: Online code formatting tool
  - **TaskTracker Mobile**: Mobile task management app
  - **BlogCMS Lite**: Lightweight CMS for bloggers
- **Internationalization**: Added Products translations to both English and Arabic i18n files
- **ProductCard Component**: Built reusable product card component with hover effects and modern design
- **Advanced Filtering**: Implemented search, category filtering, and featured products filter
- **Multiple View Modes**: Added grid and list view modes for product display
- **Pricing Display**: Support for multiple pricing types (fixed, starting, custom, contact)
- **Technology Badges**: Visual representation of technologies used in each product
- **Motion Animations**: Enhanced user experience with smooth animations and transitions

### Changed

- **Navigation Menu**: Updated to include Products link in correct order (Home, About, Services, Portfolio, Products, Contact)
- **Routing Configuration**: Added Products route to App.tsx with proper lazy loading and route wrapper
- **BasicHeader Component**: Enhanced navigation to support the new Products page

### Improved

- **Responsive Design**: Ensured full responsive design across all device sizes
- **RTL Support**: Complete right-to-left language support for Arabic users
- **User Experience**: Added call-to-action sections throughout the Products page
- **Performance**: Implemented lazy loading and code splitting for the Products page

## [0.6.3] - 2024-12-21

### Added

- Created Privacy Policy page with comprehensive privacy information and professional styling
- Created Terms of Service page with detailed terms and conditions for CodeSafir services
- Added Privacy Policy and Terms of Service routes to main application routing
- Implemented proper page structure with BasicHeader and Footer components for legal pages

### Changed

- Updated social media links in Footer component to use official CodeSafir social media accounts:
  - Facebook: https://www.facebook.com/CodeSafir
  - Instagram: https://www.instagram.com/CodeSafir/
- Enhanced footer legal section with working links to Privacy Policy and Terms of Service pages

### Improved

- Added proper RTL support for legal pages content
- Implemented responsive design for legal pages with proper typography and spacing
- Added contact information sections in both Privacy Policy and Terms of Service pages
- Used consistent styling and layout patterns across all legal pages

## [0.6.2] - 2024-12-20

### Added

- Added fullPageScreenshot field to Sanity portfolio schema for storing complete website screenshots
- Enhanced portfolio image display hierarchy: fullPageScreenshot → mainImage → placeholder
- Updated all TypeScript interfaces to support fullPageScreenshot field
- Modified Sanity GROQ queries to fetch fullPageScreenshot data

### Changed

- Implemented strategic image usage: mainImage for project cards, fullPageScreenshot for detailed views
- ProjectCard component uses mainImage for consistent card display across portfolio
- ProjectDetails component prioritizes fullPageScreenshot when available, falling back to mainImage
- Index page Recent Projects section uses mainImage for consistency with project cards
- Enhanced image hierarchy for better user experience and visual organization

### Improved

- Clear separation between card thumbnails (mainImage) and detailed screenshots (fullPageScreenshot)
- Consistent visual experience across portfolio cards and listings
- Better content organization with purpose-specific image usage
- Enhanced project detail pages with full-context website screenshots when available

## [0.6.1] - 2024-12-20

### Fixed

- Fixed Portfolio component "Cannot read properties of null (reading 'map')" error by adding proper null checks for portfolioItems and filteredProjects arrays
- Fixed Services component MIME type error by removing problematic lazy loading imports and using direct imports instead
- Fixed ProjectDetails component null map error by adding null checks for project.technologies, project.publishedAt, and relatedProjects arrays
- Enhanced Portfolio filtering logic to handle null/undefined technologies arrays safely
- Improved error handling in Portfolio component for better user experience

### Changed

- Removed console.log statements from codebase while preserving console.error for actual error handling
- Simplified Services component imports by removing Suspense and lazy loading
- Added defensive programming practices to Portfolio component with null checks
- Enhanced handling of projects without website URLs with better visual indicators and user feedback

## [0.6.0] - 2024-12-20

### Fixed

- Fixed portfolio card consistency by removing automatic featured styling for the first project
- Enhanced portfolio category filtering system with better technology mapping for:
  - Web Development: React, Next.js, Vue, Angular, HTML, CSS, JavaScript, TypeScript, Tailwind
  - E-commerce: Shopify, WooCommerce, Magento, Stripe, PayPal, and commerce-related keywords
  - CMS Solutions: WordPress, Drupal, Strapi, Contentful, Sanity
  - Mobile Apps: React Native, Flutter, Ionic, Swift, Kotlin
- Improved home page recent projects display by fixing data integration between Sanity and local portfolio data
- Enhanced project sorting logic to properly handle projects without dates by using current year as fallback
- Fixed SanityPortfolioProvider to properly merge Sanity data with local fallback data
- Improved project card image display for website screenshots with proper aspect ratio and object-contain styling

### Changed

- Portfolio page now displays all projects in a consistent grid layout without featured project section
- Improved filtering logic to categorize projects based on technologies, client names, and descriptions
- Recent Projects section now shows only Sanity-managed projects (not local fallback data)
- Project card images now use 4:3 aspect ratio with object-contain to better display full website screenshots

### Removed

- Removed "Kings of E-commerce Platform" project from local portfolio data

## [0.5.0] - 2023-08-05

### Added

- Integrated Sanity.io as a headless CMS for portfolio and blog sections
- Created content schemas for portfolio projects, blog posts, authors, and categories
- Added Sanity client for data fetching with local data fallback
- Created setup documentation for Sanity integration

## [0.4.0] - 2023-07-20

### Changed

- Updated portfolio page to include only real client projects with actual website images:
  - Tajara Tech (tajara.tech)
  - Kings of E-commerce (kingsofecommerce.com)
  - Smile Rising (smilerising.com.sa)
  - Bu Hamad Co. (buhamadco.com)
  - Richers KSA (richersksa.info)
  - ZoneArt & Workspace (zoneart.net)
  - Adam's World (adamsworld.ae)
  - Oly Jewels (olyjewels.com)
- Removed all placeholder/fictional portfolio projects
- Added image placeholders for real website screenshots

## [0.3.0] - 2023-07-15

### Added

- Portfolio page with initial client projects
- Created tasks.md to track project progress
- Created changelog.md to document changes

### Changed

- Improved UI/UX design of the About page
- Refactored footer for better UI/UX design
- Removed newsletter section from footer

### Fixed

- Fixed missing translation keys in i18next system
- Ensured all hero sections have proper translations
- Fixed responsive design issues on mobile devices

## [0.2.0] - 2023-06-20

### Added

- Next.js and Python to Technologies section
- Updated logos for Tailwind CSS and Shopify
- Implemented light/dark theme toggle
- Added RTL support for Arabic language

### Changed

- Updated hero section code area to reflect company identity
- Improved Services page to match company identity
- Enhanced UI/UX across all pages

### Fixed

- Fixed navigation issues in mobile view
- Corrected alignment issues in RTL mode
- Fixed theme persistence issues

## [0.1.0] - 2023-05-10

### Added

- Initial project setup with Vite, React 18+, TypeScript 5+
- Configured Tailwind CSS v3+ and shadcn/ui
- Set up i18next for bilingual support (English/Arabic)
- Implemented brand colors (#1A1F71, #00BFA6, #F4F4F4)
- Created basic page structure:
  - Home page with hero section
  - Services page
  - About page
  - Contact page
- Added responsive layout components (Header, Footer)
